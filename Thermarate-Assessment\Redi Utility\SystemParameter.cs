﻿using System;
using System.Data.SqlClient;

namespace RediSoftware.Redi_Utility
{
    public class SystemParameter
    {
        public const Int16 SysParmsCachePeriodMinutes = 60;
        public enum ParameterCodes
        {
            CurrentBatchDate,
            LastBatchDate,
            CurrentOnlineDate,
            GSTPctAus,
            CurrentFinYear,
            IsEndOfMonth,
            FloorHeightTolerance
        }

        public static string GetStringParm(ParameterCodes ParmCode)
        {
            return GetStringParm(ParmCode, false, false);
        }

        public static string GetStringParm(string ParmCode, bool bypassCache = false)
        {
            return GetStringParm(ParmCode, bypassCache, false);
        }

        public static string GetStringParm(ParameterCodes ParmCode, Boolean BypassCache)
        {
            return GetStringParm(ParmCode, BypassCache, false);
        }

        public static string GetStringParm(ParameterCodes ParmCode, Boolean BypassCache, Boolean IgnoreNotFound)
        {
            return GetStringParm(ParmCode.ToString("G"), BypassCache, IgnoreNotFound);
        }

        public static string GetStringParm(string ParmCode, Boolean BypassCache, Boolean IgnoreNotFound)
        {
            string parmString = "";

            if (!BypassCache)
                parmString = (string)CacheHandler.LookupCache(CacheHandler.DataType_SysParm, ParmCode);

            if (BypassCache || string.IsNullOrEmpty(parmString))
            {
                try
                {
                    using (SqlText select = new SqlText(
                                   "Select ParmString " +
                                   "from [RSS_SystemParameters] " +
                                   "Where ParmCode = @ParmCode; "))
                    {
                        select.AddParameter("@ParmCode", ParmCode);

                        parmString = select.ExecuteScalar() as string;
                        if (string.IsNullOrEmpty(parmString))
                        {
                            if (IgnoreNotFound)
                            {
                                parmString = "";
                                return parmString;
                            }
                            throw new Exception("Parameter not found on RSS_SystemParameters: " + ParmCode);
                        }
                        CacheHandler.SaveToCache(CacheHandler.DataType_SysParm, ParmCode, parmString, SysParmsCachePeriodMinutes);
                    }
                }
                catch (SqlException)
                {
                    throw;
                }
                catch (Exception)
                {
                    parmString = "";
                    return parmString;
                }
            }

            return parmString;

        }

        public static Int64 GetIntParm(ParameterCodes ParmCode)
        {
            return GetIntParm(ParmCode, false);
        }

        public static Int64 GetIntParm(string ParmCode)
        {
            return GetIntParm(ParmCode, false);
        }

        public static Int64 GetIntParm(ParameterCodes ParmCode, Boolean BypassCache)
        {
            return GetIntParm(ParmCode.ToString("G"), BypassCache);
        }

        public static Int64 GetIntParm(string ParmCode, Boolean BypassCache)
        {
            Int64 parmInt;
            Nullable<Int64> parmIntNull;

            try
            {
                if (!BypassCache)
                {
                    return 0;
                    parmIntNull = (Int64)CacheHandler.LookupCache(CacheHandler.DataType_SysParm, ParmCode);
                    parmInt = (Int64)parmIntNull;
                    return parmInt;
                }
            }
            catch (Exception)
            {

            }

            try
            {
                using (SqlText select = new SqlText(
                               "Select ParmInt " +
                               "from [RSS_SystemParameters] " +
                               "Where ParmCode = @ParmCode; "))
                {
                    select.AddParameter("@ParmCode", ParmCode);

                    object result = select.ExecuteScalar();
                    
                    if (result == null)
                    {
                        throw new Exception("Parameter not found on RSS_SystemParameters: " + ParmCode);
                    }

                    parmInt = Convert.ToInt64(result);

                    CacheHandler.SaveToCache(CacheHandler.DataType_SysParm, ParmCode, parmInt, SysParmsCachePeriodMinutes);
                }
            }
            catch (SqlException)
            {
                throw;
            }

            return parmInt;

        }

        /// <summary>
        /// Updates an existing SystemParameter with the given value, or if it does not
        /// yet exist, inserts the given parmCode into the database with the given value.
        /// </summary>
        internal static void UpdateIntParm(string parmCode, int value)
        {
            try
            {
                using (var existsCmd = new SqlText($"SELECT ParmCode FROM dbo.RSS_SystemParameters WHERE ParmCode = '{parmCode}'"))
                {
                    var exists = existsCmd.ExecuteScalar() as string;

                    if (string.IsNullOrEmpty(exists))
                    {
                        // This parmCode doesn't exist yet, so insert it
                        using (var insertCmd = new SqlText($"INSERT INTO dbo.RSS_SystemParameters (ParmCode, ParmInt) VALUES ('{parmCode}', {value});"))
                        {
                            insertCmd.ExecuteNonQuery();
                        }
                    }
                    else
                    {
                        // This parm code exists, so update it
                        using (var updateCmd = new SqlText($"UPDATE dbo.RSS_SystemParameters SET ParmInt = {value} WHERE ParmCode = '{parmCode}';"))
                        {
                            updateCmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch
            {
                throw;
            }
            
        }

        public static DateTime GetDateTimeParm(ParameterCodes ParmCode)
        {
            return GetDateTimeParm(ParmCode, false);
        }

        public static DateTime GetDateTimeParm(string ParmCode)
        {
            return GetDateTimeParm(ParmCode, false);
        }

        public static DateTime GetDateTimeParm(ParameterCodes ParmCode, Boolean BypassCache)
        {
            return GetDateTimeParm(ParmCode.ToString("G"), BypassCache);
        }

        public static DateTime GetDateTimeParm(string ParmCode, Boolean BypassCache)
        {
            DateTime parmDateTime = DateTime.MinValue;

            if (!BypassCache)
                parmDateTime = (DateTime)CacheHandler.LookupCache(CacheHandler.DataType_SysParm, ParmCode);

            if (BypassCache || parmDateTime == null)
            {
                try
                {
                    using (SqlText select = new SqlText(
                                   "Select ParmDateTime " +
                                   "from [RSS_SystemParameters] " +
                                   "Where ParmCode = @ParmCode; "))
                    {
                        select.AddParameter("@ParmCode", ParmCode);

                        parmDateTime = Convert.ToDateTime(select.ExecuteScalar());
                        if (parmDateTime == null)
                        {
                            throw new Exception("Parameter not found on RSS_SystemParameters: " + ParmCode);
                        }
                        CacheHandler.SaveToCache(CacheHandler.DataType_SysParm, ParmCode, parmDateTime, SysParmsCachePeriodMinutes);
                    }
                }
                catch (SqlException)
                {
                    throw;
                }
            }

            return parmDateTime;

        }

        public static void UpdateDateTimeParm(ParameterCodes ParmCode, DateTime NewDateTime)
        {
            UpdateDateTimeParm(ParmCode.ToString("G"), NewDateTime);
        }

        public static void UpdateDateTimeParm(string ParmCode, DateTime NewDateTime)
        {
            try
            {
                using (SqlText update = new SqlText(
                                "Update [RSS_SystemParameters] " +
                                " Set ParmDateTime = @ParmDateTime " +
                                "Where ParmCode = @ParmCode; "))
                {
                    update.AddParameter("@ParmCode", ParmCode);
                    update.AddParameter("@ParmDateTime", NewDateTime);

                    update.ExecuteNonQuery();
                    CacheHandler.SaveToCache(CacheHandler.DataType_SysParm, ParmCode, NewDateTime, SysParmsCachePeriodMinutes);
                }
            }
            catch (SqlException)
            {
                throw;
            }


        }

        public static void UpdateStringParm(string ParmCode, string newString)
        {
            try
            {
                using (SqlText update = new SqlText(
                                "Update [RSS_SystemParameters] " +
                                " Set ParmString = @ParmString " +
                                "Where ParmCode = @ParmCode; "))
                {
                    update.AddParameter("@ParmCode", ParmCode);
                    update.AddParameter("@ParmString", newString);

                    update.ExecuteNonQuery();
                    CacheHandler.SaveToCache(CacheHandler.DataType_SysParm, ParmCode, newString, SysParmsCachePeriodMinutes);
                }
            }
            catch (SqlException)
            {
                throw;
            }


        }

    }

    class SystemParameterModel
    {
        public string ParmCode { get; set; }
        public Int64 ParmInt { get; set; }
        public string ParmString { get; set; }
        public DateTime ParmDateTime { get; set; }
    }
}
