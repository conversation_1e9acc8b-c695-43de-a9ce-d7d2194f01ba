using Newtonsoft.Json.Linq;
using RediSoftware.Common;
using System.Collections.Generic;

namespace RediSoftware.Dtos
{
    public class ConstructionFilterDataDto
    {
        public List<MultiFilterFieldDto> fields { get; set; }
        public Dictionary<string, List<FilterOption>> filterOptions { get; set; }
        public JObject appliedFilters { get; set; }
        public List<object> searchFilter { get; set; }
        public string type { get; set; }
        public PagingParameters paging { get; set; }
    }

    public class ConstructionFilterOptionsDto
    {
        public List<MultiFilterFieldDto> fieldsList { get; set; }
        public string type { get; set; }
    }
}
