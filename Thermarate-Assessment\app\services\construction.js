// Name: constructionservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'constructionservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', constructionservice]);

    function constructionservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'construction/';

        let constructionCategories = [];
        let openingCategories = [];

        function handleSuccess(response, popupMessage = null) {
            if (response != null && response.data != undefined && response.data != null) {
                useListCache = true;

                if (popupMessage != null) {
                    log.logSuccess(popupMessage);
                }

                return response.data;
            }
            else {
                return null;
            }
        }

        function handleFail(error, message) {
            var msg = `${message}: ${error}`;
            log.logError(msg, error, null, true);
            throw error; // so caller can see it
        }

        function getListV2(type, forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'GetV2';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { type, fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(handleSuccess, (error) => {

                if (error.status == 0 || error.status == -1) {
                    return;
                } else
                    handleFail(error, "Error getting construction list");
            });
        }

        /**
         * Bulk returns ALL constructions (surfaces and openings) mainly for use in typeahead in
         * construction tabs (i.e. where we don't want to paginate). Amount of data should still be
         * reasonably minimal but if it becomes a problem maybe hit the server for individual queries...
         */
        function getAll() {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'GetAll';

            //Get error List from the Server
            return $http({
                url: wkUrl,
                method: 'GET',
                timeout: canceller.promise,
                cache: true,
            }).then(handleSuccess, (error) => {

                if (error.status == 0 || error.status == -1) {
                    return;
                } else
                    handleFail(error, "Error getting construction list");
            });
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

        function getConstruction(constructionId, type) {

            return $http({
                url: baseUrl + 'Get',
                params: { constructionId, type },
                method: 'GET',
            }).then(handleSuccess, (error) => handleFail(error, "Error getting Construction"));
        }

        /* Adds a Surface (Template) to our Surfaces table. */
        function createSurface(data) {
            var url = baseUrl + 'CreateSurface';
            return $http.post(url, data).then(handleSuccess, (e) => handleFail(e, "Error creating Surface"));
        }

        function updateSurface(surface) {
            var url = baseUrl + 'UpdateSurface';
            return $http.post(url, surface).then(
                (r) => handleSuccess(r, "Construction Updated"),
                (e) => handleFail(e, "Error updating Surface"));
        }

        /** Adds an Opening (Template) to our Opening table. */
        function createOpening(opening) {
            var url = baseUrl + 'CreateOpening';
            return $http.post(url, opening).then(handleSuccess, (e) => handleFail(e, "Error creating Surface"));
        }

        function updateOpening(opening) {
            var url = baseUrl + 'UpdateOpening';
            return $http.post(url, opening).then((r) => handleSuccess(r, "Construction Updated"), (e) => handleFail(e, "Error updating Surface"));
        }

        function copyConstruction(constructionId, type, showSuccessMessage = true) {
            return $http({
                url: baseUrl + 'Copy',
                params: { constructionId, type },
                method: 'POST',
            }).then(
                (r) => handleSuccess(r, showSuccessMessage ? "Construction copied successfully" : null),
                (e) => handleFail(e, "Error copying Construction"));
        }

        function deleteConstruction(constructionId, type, showSuccessMessage = true) {
            return $http({
                url: baseUrl + 'Delete',
                params: { constructionId, type },
                method: 'POST',
            }).then(
                (r) => handleSuccess(r, showSuccessMessage ? "Construction deleted successfully" : null),
                (e) => handleFail(e, "Error deleting Construction"));
        }

        function undoDeleteConstruction(constructionId, type) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { constructionId, type },
                method: 'POST',
            }).then(
                (r) => handleSuccess(r, "Construction restored successfully."),
                (e) => handleFail(e, "Error restoring Construction"));
        }

        /** ALL construction categories (inc all surface + opening sub-categories). */
        function getConstructionCategoryList() {

            return $http({
                url: baseUrl + 'GetConstructionCategoryList',
                method: 'GET',
                cache: true,
            }).then((r) => {

                constructionCategories = r.data
                    .filter(x => x.type == 'surface' &&
                        (x.constructionCategoryCode != "ExteriorDoor" &&
                            x.constructionCategoryCode != "InteriorDoor"));

                openingCategories = r.data
                    .filter(x => x.type != 'surface' ||
                        (x.constructionCategoryCode == "ExteriorDoor" ||
                            x.constructionCategoryCode == "InteriorDoor" ||
                            x.constructionCategoryCode == "PermanentOpening"));

                return r.data;

            }, (error) => handleFail(error, "Error getting Adjacency List"));
        }

        function getConstructionSubCategoryList() {

            return $http({
                url: baseUrl + 'GetConstructionSubCategoryList',
                method: 'GET',
                cache: true,
            }).then(
                (r) =>  r.data,
                (error) => handleFail(error, "Error getting Adjacency List")
            );
        }

        function getUnitOfMeasureList() {

            return $http({
                url: baseUrl + 'GetUnitOfMeasureList',
                method: 'GET',
                cache: true,
            }).then(r => r.data,
                (error) => handleFail(error, "Error getting Unit Of Measure List"));
        }


        function getAdjacencyList() {

            return $http({
                url: baseUrl + 'GetAdjacencyList',
                method: 'GET',
                cache: true,
            }).then(r => r.data,
              (error) => handleFail(error, "Error getting Adjacency List"));
        }

        function getAirCavityList() {

            return $http({
                url: baseUrl + 'GetAirCavityList',
                method: 'GET',
                cache: true,
            }).then(r => r.data,
                (error) => handleFail(error, "Error getting Air Cavity List"));
        }

        function getOpeningStyleList() {

            return $http({
                url: baseUrl + 'GetOpeningStyleList',
                method: 'GET',
                cache: true,
            }).then(r => r.data,
                (error) => handleFail(error, "Error getting Opening Style List"));
        }

        function getNccOpeningStyleList() {

            return $http({
                url: baseUrl + 'GetNccOpeningStyleList',
                method: 'GET',
                cache: true,
            }).then(r => r.data,
                (error) => handleFail(error, "Error getting NCC Opening Style List"));
        }

        function getFrameMaterialList() {

            return $http({
                url: baseUrl + 'GetFrameMaterialList',
                method: 'GET',
                cache: true,
            }).then(r => r.data,
                (error) => handleFail(error, "Error getting Frame Material List"));
        }

        function getGlassTypeList() {

            return $http({
                url: baseUrl + 'GetGlassTypeList',
                method: 'GET',
                cache: true,
            }).then(r => r.data,
                (error) => handleFail(error, "Error getting Glass Type List"));
        }

        function getGlassColourList() {

            return $http({
                url: baseUrl + 'GetGlassColourList',
                method: 'GET',
                cache: true,
            }).then(r => r.data,
                (error) => handleFail(error, "Error getting Glass Colour List"));
        }

        /**
         *  Checks to see if the given building contains constructions of the specific category..
         * @param {any} category
         * @param {any} building
         *
         * @returns {boolean} True if their are constructions of that category, else false.
         */
        function checkBuildingContainsConstructionOfCategory(category, building) {

            let found = getConstructionsForSpecificCategory(category, building);
            return found != null && found.length > 0;
        }

        /**
         * Just returns 'building.surfaces' or 'building.openings' depending on the category.
         *
         *
         * @param {any} category Category OBJECT, not code!
         * @param {any} building The building whose constructions you want returned.
         */
        function getAllConstructionsForCategoryType(category, building) {

            if (building == null)
                return [];

            return category.type === "surface" || category.type === "permanentopening"
                ? building.surfaces
                : building.openings;
        }

        /** Returns only constructions matching the EXACT category supplied. */
        function getConstructionsForSpecificCategory(category, building) {
            let list = getAllConstructionsForCategoryType(category, building);


            if (list == null || list.length == 0)
                return [];

            let filtered = list
                .filter(x => x.category?.constructionCategoryCode == category?.constructionCategoryCode);

            return filtered;
        }


        async function applyTemplate(option, building, template) {

            if(template == null)
                return;

            let type = template.templateType;

            // CLEAR EXISTING + USE TIMEOUTS TO AVOID WEIRD UI DISPLAY BUGS.
            await common.delay(100);

            // Only apply data we care about for the given template type. Other data
            // (which may be vestigial, depending on how often Alistar changes his mind)
            // will be ignored.
            let externalData = dataWeCareAbout(template, template.categoriesWithExternalData);
            building.categoriesWithExternalData = angular.merge(building.categoriesWithExternalData, externalData);

            let categoriesNotRequired = dataWeCareAbout(template, template.categoriesNotRequired);
            building.categoriesNotRequired = angular.merge(building.categoriesNotRequired, categoriesNotRequired);
            building.zoneTypesNotApplicable = angular.merge(building.zoneTypesNotApplicable, template.zoneTypesNotApplicable);

            let surfacesToRetain = template.templateType === 'construction'
                ? building.surfaces.filter(x => x.category.constructionCategoryCode === "ExteriorDoor" ||
                    x.category.constructionCategoryCode === "InteriorDoor")
                : building.surfaces.filter(x => !(x.category.constructionCategoryCode === "ExteriorDoor"||
                    x.category.constructionCategoryCode === "InteriorDoor"));


            let openingsToRetain = template.templateType === 'construction'
                ? building.openings
                : [];

            // Since we also use this 'apply template' for our 'copy baseline / copy option' functionality, we need to
            // also trim what we don't want to use from the template.
            const templateSurfacesToCopy = template.templateType === 'construction'
                ? template.surfaces.filter(x => !(x.category.constructionCategoryCode === "ExteriorDoor"||
                    x.category.constructionCategoryCode === "InteriorDoor"))
                : template.surfaces.filter(x => x.category.constructionCategoryCode === "ExteriorDoor" ||
                    x.category.constructionCategoryCode === "InteriorDoor");


            const templateOpeningsToCopy = template.templateType === 'construction'
                ? []
                : template.openings;


            building.surfaces = angular.copy([...templateSurfacesToCopy, ...surfacesToRetain]);
            building.openings = angular.copy([...templateOpeningsToCopy, ...openingsToRetain]);

            // Set all sources to manual on copied elements, so that they may be fully edited (even if they came from
            // a scratch file originally)
            building.surfaces.forEach(setSourceToManual);
            building.openings.forEach(setSourceToManual);

            if (type === 'construction') {
                building.lowestLivingAreaFloorType = template.lowestLivingAreaFloorType;
                building.classification = template.classification;
                building.masonryWalls = template.masonryWalls;
            } else {
                building.openingSpecification = template.openingSpecification;
            }

            building[type + "TemplateTitle"] = template.templateName;
            building[type + "TemplateId"] = template.buildingConstructionTemplateId;

            function setSourceToManual(parent) {
                parent.source = 'manual';
                parent.elements.forEach(ele => ele.source = 'manual');
            }
        }

        /**
         * Uploads an excel file to our server which is then processed into the construction
         * database
         *
         * @param {any} stream The excel file. Should be either the construction OR opening master database excel file.
         * @param {string} type Specify "construction" or "opening" - anything else will fail.
         * @param {boolean} forceImport If false (the default) the file will not be processed into the DB if any warnings
         *                              or errors are encountered. Pass a value of true if you wish import anyway.
         */
        function uploadConstructionDatabase(type, stream, forceImport = false) {

            let url = baseUrl + 'UploadConstructionDatabase';
            return $http({
                url: url,
                method: "POST",
                data: stream,
                params: { type, forceImport },
                headers: {
                    "Content-Type": "application/vnd.ms-excel"
                }
            }).then(
                (data) => handleSuccess(data),
                (error) => handleFail(error, "Error processing excel file!")
            );
        }

        /**
         * Exports the construction database to an Excel file in the same format as expected for imports
         *
         * @param {string} type Specify "construction" or "opening" - anything else will fail.
         * @param {Array} ids Optional list of construction IDs to export. If not provided, all non-deleted constructions will be exported.
         */
        function exportConstructionDatabase(type, ids) {
            let url = baseUrl + 'ExportConstructionDatabase';

            return $http({
                url: url,
                method: "POST",
                params: { type },
                data: ids,
                responseType: "blob"
            }).then(
                (response) => {
                    // Create a dummy anchor element with a data uri and simulate a click on it.
                    // This will show the download pop-up to the user.
                    var a = window.document.createElement('a');
                    a.href = window.URL.createObjectURL(response.data);
                    a.download = type + "Database.xlsx";
                    document.body.appendChild(a);
                    a.click();

                    // Finally, remove our anchor from the dom.
                    document.body.removeChild(a);

                    log.logSuccess(type + " Database exported successfully. See downloads.");
                    return response;
                },
                (error) => handleFail(error, "Error exporting " + type + " database!")
            );
        }


        /**
         * This function is used purely so that in future if Alistair changes his mind about which types
         * of constructions belong in which templates, we will filter which categories of data to apply
         * based on the current rules that apply, rather than those that applied when the template was
         * created (That could cause some reaaallly subtle bugs).
         *
         * @param {any} template The template to pull the category from
         * @param {any} source The source of data we are extracting from.
         */
        function dataWeCareAbout(template, source) {

            let categories = [];

            if (template.templateType == 'construction')
                categories = constructionCategories;
            else if (template.templateType == 'opening')
                categories = openingCategories;


            let dataWeWant = {};
            for (let i = 0; i < categories.length; i++) {

                let code = categories[i].constructionCategoryCode.toLowerCase();
                dataWeWant[code] = source[code] ?? false; // Default to 'false' if undefined for e.g. blank templates.
            }

            return dataWeWant;
        }

        const chenathConstructionRanges = [
            { title: "Windows",                     range: { minimum:   1, maximum:  20 } },
            { title: "Skylights",                   range: { minimum:  21, maximum:  40 } },
            { title: "External Walls & Doors",      range: { minimum:  41, maximum:  90 } },
            { title: "Roofs",                       range: { minimum:  91, maximum: 190 } },
            { title: "External Floors   ",          range: { minimum: 191, maximum: 240 } },
            { title: "Floors to other Zones",       range: { minimum: 241, maximum: 340 } },
            { title: "Ceilings to other Zones",     range: { minimum: 341, maximum: 440 } },
            { title: "Walls to other Zones",        range: { minimum: 441, maximum: 490 } },
            { title: "Walls within Zones",          range: { minimum: 491, maximum: 520 } },
            { title: "Floors to Neighbour",         range: { minimum: 521, maximum: 550 } },
            { title: "Ceilings to Neighbour",       range: { minimum: 551, maximum: 580 } },
            { title: "Walls to Neighbour",          range: { minimum: 581, maximum: 610 } },
        ];

        const shadingOutcomeMap = {
            "": 0,
            Minimal: 1,
            Moderate: 2,
            High: 3,
            Obstructive: 4
        }

        // Compares and returns the highest shading string value (ie. Minimal, Moderate, High, Obstructive).
        function greaterShading(a, b, c = null) {

            if(c == null) {

                if(a == null && b == null)
                    return "";

                if(a == null && b != null)
                    return b;

                if(b == null && a != null)
                    return a;

                return shadingOutcomeMap[a] > shadingOutcomeMap[b]
                    ? a
                    : b;

            } else {

                let aa = greaterShading(a, b);
                let bb = greaterShading(a, c);

                return greaterShading(aa, bb);
            }
        }


        function getListMultiFiltered(pageSize, pageIndex, sort, fields, filterOptions, appliedFilters, searchFilter, type) {
            var url = baseUrl + 'GetMultiFiltered';
            let filterData = { fields, filterOptions, appliedFilters, searchFilter, type };
            filterData.paging = common.buildqueryparameters.build(null, pageSize, pageIndex, sort?.field != null ? [sort] : null, searchFilter);
            return $http.post(url, filterData).then(success, fail);
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Multi-Filtered Constructions: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getMultiFilterOptions(fieldsList, type) {
            var url = baseUrl + 'GetMultiFilterOptions';
            return $http.post(url, { fieldsList, type }).then(success, fail);
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Multi-Filter Options: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getFilterCountData(fields, filterOptions, appliedFilters, searchFilter, type) {
            var url = baseUrl + 'GetFilterCountData';
            let filterData = { fields, filterOptions, appliedFilters, searchFilter, type };
            return $http.post(url, filterData).then(success, fail);
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting Filter Count Data: " + error;
                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        let service = {
            /* These are the operations that are available from this service. */
            getAll,
            getListV2,
            getListCancel,
            currentFilter: function () { return currentFilter },
            getConstruction,
            createSurface,
            updateSurface,
            createOpening,
            updateOpening,
            copyConstruction,
            deleteConstruction,
            undoDeleteConstruction,
            getConstructionCategoryList,
            getConstructionSubCategoryList,
            getAdjacencyList,
            getUnitOfMeasureList,
            getAirCavityList,
            getOpeningStyleList,
            getNccOpeningStyleList,
            getFrameMaterialList,
            getGlassTypeList,
            getGlassColourList,
            uploadConstructionDatabase,
            exportConstructionDatabase,
            getListMultiFiltered,
            getMultiFilterOptions,
            getFilterCountData,

            // These don't call the server.
            checkBuildingContainsConstructionOfCategory,
            getAllConstructionsForCategoryType,
            getConstructionsForSpecificCategory,
            chenathConstructionRanges,
            constructionCategories: () => constructionCategories,
            openingCategories: () => openingCategories,
            applyTemplate,
            shadingOutcomeMap,
            greaterShading,
            setIsFavourite,
        };

        function setIsFavourite(constructionId, isFavourite, type) {
            if (!type) {
                type = 'surface';
            }

            if (type === 'surface') {
                return $http({
                    url: baseUrl + 'SetSurfaceIsFavourite',
                    params: { constructionId, isFavourite },
                    method: 'POST',
                }).then(
                    (r) => handleSuccess(r),
                    (e) => handleFail(e, "Error setting favourite status for Surface Construction")
                );
            } else if (type === 'opening') {
                return $http({
                    url: baseUrl + 'SetOpeningIsFavourite',
                    params: { constructionId, isFavourite },
                    method: 'POST',
                }).then(
                    (r) => handleSuccess(r),
                    (e) => handleFail(e, "Error setting favourite status for Opening Construction")
                );
            } else {
                return $http({
                    url: baseUrl + 'SetIsFavourite',
                    params: { constructionId, isFavourite, type },
                    method: 'POST',
                }).then(
                    (r) => handleSuccess(r),
                    (e) => handleFail(e, "Error setting favourite status for Construction")
                );
            }
        }

        return service;

    }
})();
